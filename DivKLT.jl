"""
MODULE JULIA AUTONOME - DivKLT
==============================

MÉTRIQUE AUTONOME - DivKLT (PRIORITÉ 2)
FORMULE 6B : Divergence KL Observée vs T<PERSON><PERSON>orique (VERSION CORRIGÉE)
Mesure l'écart entre les fréquences réellement observées et les probabilités théoriques INDEX5.

FORMULE : DivKLT = ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "approximative" (modèle)

USAGE :
    using DivKLT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_A_BANKER"]
    result = calculer_formule6B_divergence_kl_theo(formulas, sequence, 3)

DÉPENDANCES : AUCUNE - Module complètement autonome
"""

module DivKLT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - DivKLT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule la divergence de Kullback-Leibler entre la distribution observée et la distribution théorique INDEX5.

FORMULE : DivKLT_n = ∑_{x ∈ E_n} p_obs(x) × log₂(p_obs(x)/p_theo(x))
Où :
- E_n = ensemble des valeurs INDEX5 distinctes observées dans [1:n]
- p_obs(x) = fréquence observée de x dans [1:n]
- p_theo(x) = probabilité théorique INDEX5 de x

INTERPRÉTATION :
- DivKLT = 0 : Distribution observée = distribution théorique (parfait)
- DivKLT > 0 : Distribution observée différente de la théorique
- Plus DivKLT est élevé, plus l'écart est important

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Divergence KL en bits (si base=2.0), toujours ≥ 0
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL : ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
    divergence = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Divergence KL standard : p_obs × log₂(p_obs/p_theo)
            # Formule officielle : D_KL(P||Q) = ∑ p(x) log(p(x)/q(x))
            divergence += p_obs * (log(p_obs / p_theo) / log(formulas.base))
        end
    end

    return divergence
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule6B_divergence_kl_theo

end # module DivKLT
