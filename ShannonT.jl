"""
MODULE JULIA AUTONOME - ShannonT
================================

MÉTRIQUE AUTONOME - ShannonT (PRIORITÉ 5)
FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.

USAGE :
    using ShannonT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule1B_shannon_jointe_theo(formulas, sequence, 3)

DÉPENDANCES : AUCUNE - Module complètement autonome
"""

module ShannonT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - ShannonT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie de Shannon théorique pour une séquence croissante [main 1 : main n].

FORMULE : ShannonT_n = -∑_{x ∈ E_n} p_theo(x) × log₂(p_theo(x))
Où E_n = ensemble des valeurs INDEX5 distinctes observées dans [1:n]

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Entropie de Shannon théorique en bits (si base=2.0)
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p_theo(x) log₂(p_theo(x))
            # Formule officielle de Shannon avec probabilités théoriques
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule1B_shannon_jointe_theo

end # module ShannonT
