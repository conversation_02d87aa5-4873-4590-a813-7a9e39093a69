# Tests et Validation en Julia - Bonnes Pratiques

## 1. Structure des Tests

### Organisation des Fichiers de Test
```
test/
├── runtests.jl              # Point d'entrée principal
├── test_types.jl            # Tests des types et structures
├── test_functions.jl        # Tests des fonctions principales
├── test_utils.jl            # Tests des utilitaires
├── test_performance.jl      # Tests de performance
└── fixtures/                # Données de test
    ├── sample_data.json
    └── expected_results.json
```

### Point d'Entrée Principal (runtests.jl)
```julia
using MyPackage
using Test

@testset "MyPackage.jl" begin
    @testset "Types and Structures" begin
        include("test_types.jl")
    end
    
    @testset "Core Functions" begin
        include("test_functions.jl")
    end
    
    @testset "Utilities" begin
        include("test_utils.jl")
    end
    
    @testset "Performance" begin
        include("test_performance.jl")
    end
end
```

## 2. Types de Tests

### Tests Unitaires
```julia
@testset "EntropyAnalyzer" begin
    @testset "Construction" begin
        # Test construction normale
        analyzer = EntropyAnalyzer(2.0, 1e-12)
        @test analyzer.base == 2.0
        @test analyzer.epsilon == 1e-12
        
        # Test construction avec valeurs par défaut
        analyzer_default = EntropyAnalyzer()
        @test analyzer_default.base == 2.0
        @test analyzer_default.epsilon == 1e-12
        
        # Test construction avec types différents
        analyzer_f32 = EntropyAnalyzer{Float32}(2.0f0, 1f-12)
        @test typeof(analyzer_f32.base) == Float32
    end
    
    @testset "Validation" begin
        # Test validation des paramètres
        @test_throws ArgumentError EntropyAnalyzer(-1.0, 1e-12)
        @test_throws ArgumentError EntropyAnalyzer(2.0, -1e-12)
        @test_throws ArgumentError EntropyAnalyzer(1.0, 1e-12)  # base = 1 invalide
    end
end
```

### Tests d'Intégration
```julia
@testset "Full Analysis Pipeline" begin
    # Données de test
    data = [1, 2, 2, 3, 3, 3]
    
    # Pipeline complet
    analyzer = EntropyAnalyzer(2.0)
    preprocessed = preprocess_data(data)
    entropy = calculate_entropy(analyzer, preprocessed)
    result = generate_report(entropy, analyzer)
    
    # Vérifications
    @test entropy > 0
    @test entropy < log2(length(unique(data)))
    @test haskey(result, :entropy)
    @test haskey(result, :analyzer_config)
end
```

### Tests de Propriétés
```julia
using Random

@testset "Property-based Tests" begin
    @testset "Entropy Properties" begin
        analyzer = EntropyAnalyzer(2.0)
        
        # Propriété: entropie >= 0
        for _ in 1:100
            data = rand(1:10, rand(10:100))
            entropy = calculate_entropy(analyzer, data)
            @test entropy >= 0
        end
        
        # Propriété: entropie maximale pour distribution uniforme
        uniform_data = repeat(1:10, 10)
        shuffle!(uniform_data)
        entropy_uniform = calculate_entropy(analyzer, uniform_data)
        
        # Entropie d'une distribution non-uniforme doit être <= uniforme
        skewed_data = vcat(repeat([1], 50), repeat(2:10, 5))
        entropy_skewed = calculate_entropy(analyzer, skewed_data)
        @test entropy_skewed <= entropy_uniform
    end
end
```

## 3. Assertions et Vérifications

### Types d'Assertions
```julia
@testset "Assertion Types" begin
    # Égalité exacte
    @test calculate_sum([1, 2, 3]) == 6
    
    # Égalité approximative
    @test calculate_pi_approximation() ≈ π
    @test calculate_pi_approximation() ≈ π atol=1e-10
    @test calculate_pi_approximation() ≈ π rtol=1e-6
    
    # Comparaisons
    @test calculate_entropy(data) > 0
    @test length(result) >= 1
    
    # Types
    @test result isa Vector{Float64}
    @test typeof(analyzer) <: AbstractAnalyzer
    
    # Exceptions
    @test_throws ArgumentError invalid_function(bad_input)
    @test_throws BoundsError array[100]
    
    # Warnings
    @test_logs (:warn, "Deprecated function") old_function()
    
    # Allocations (avec BenchmarkTools)
    @test (@allocated my_function(data)) == 0
end
```

### Tests de Performance
```julia
using BenchmarkTools

@testset "Performance Tests" begin
    data = rand(1000)
    analyzer = EntropyAnalyzer(2.0)
    
    @testset "Speed Requirements" begin
        # Test que la fonction est assez rapide
        @test (@elapsed calculate_entropy(analyzer, data)) < 0.1
        
        # Test avec BenchmarkTools
        benchmark = @benchmark calculate_entropy($analyzer, $data)
        @test median(benchmark).time < 1_000_000  # < 1ms
    end
    
    @testset "Memory Allocation" begin
        # Test qu'il n'y a pas d'allocations inattendues
        allocs = @allocated calculate_entropy(analyzer, data)
        @test allocs < 1000  # Moins de 1KB d'allocations
    end
    
    @testset "Scaling" begin
        # Test que la complexité est correcte
        small_data = rand(100)
        large_data = rand(10000)
        
        time_small = @elapsed calculate_entropy(analyzer, small_data)
        time_large = @elapsed calculate_entropy(analyzer, large_data)
        
        # Complexité O(n) attendue
        @test time_large / time_small < 200  # Facteur raisonnable
    end
end
```

## 4. Données de Test

### Fixtures et Données de Test
```julia
# Constantes de test
const TEST_DATA_SIMPLE = [1, 2, 2, 3, 3, 3]
const TEST_DATA_UNIFORM = repeat(1:10, 10)
const TEST_ENTROPY_EXPECTED = 1.4591479170272448  # Valeur précalculée

@testset "Test Data Management" begin
    @testset "Simple Cases" begin
        # Cas simples avec résultats connus
        @test calculate_entropy(EntropyAnalyzer(2.0), [1, 1, 1, 1]) == 0.0
        @test calculate_entropy(EntropyAnalyzer(2.0), [1, 2]) ≈ 1.0
    end
    
    @testset "Edge Cases" begin
        # Cas limites
        @test_throws ArgumentError calculate_entropy(analyzer, Int[])
        @test calculate_entropy(analyzer, [42]) == 0.0
        
        # Données avec valeurs manquantes
        data_with_missing = [1, 2, missing, 3]
        @test_throws MethodError calculate_entropy(analyzer, data_with_missing)
    end
end
```

### Génération de Données de Test
```julia
function generate_test_data(distribution_type::Symbol, size::Int)
    if distribution_type == :uniform
        return rand(1:10, size)
    elseif distribution_type == :normal
        return round.(Int, randn(size) * 2 .+ 5)
    elseif distribution_type == :skewed
        return vcat(repeat([1], size÷2), rand(2:10, size÷2))
    else
        throw(ArgumentError("Unknown distribution type: $distribution_type"))
    end
end

@testset "Generated Test Data" begin
    for dist in [:uniform, :normal, :skewed]
        for size in [10, 100, 1000]
            data = generate_test_data(dist, size)
            entropy = calculate_entropy(EntropyAnalyzer(2.0), data)
            
            @test entropy >= 0
            @test entropy <= log2(length(unique(data)))
            @test length(data) == size
        end
    end
end
```

## 5. Mocking et Stubbing

### Mocking Simple
```julia
# Mock d'une fonction externe
original_random = Random.rand
mock_values = [0.1, 0.5, 0.9, 0.2]
mock_index = Ref(1)

function mock_rand()
    val = mock_values[mock_index[]]
    mock_index[] = mod1(mock_index[] + 1, length(mock_values))
    return val
end

@testset "Mocked Functions" begin
    # Remplacer temporairement la fonction
    Random.rand = mock_rand
    
    try
        result = function_using_random()
        @test result == expected_with_mock_values
    finally
        # Restaurer la fonction originale
        Random.rand = original_random
    end
end
```

### Test d'Interfaces
```julia
# Test qu'un type implémente correctement une interface
function test_analyzer_interface(analyzer_type::Type{<:AbstractAnalyzer})
    @testset "Analyzer Interface: $(analyzer_type)" begin
        analyzer = analyzer_type()
        test_data = [1, 2, 2, 3]
        
        # Test que les méthodes requises existent
        @test hasmethod(calculate_entropy, (analyzer_type, Vector{Int}))
        @test hasmethod(reset!, (analyzer_type,))
        
        # Test du comportement
        entropy = calculate_entropy(analyzer, test_data)
        @test entropy isa Real
        @test entropy >= 0
        
        # Test de reset
        reset!(analyzer)
        entropy2 = calculate_entropy(analyzer, test_data)
        @test entropy == entropy2  # Résultat identique après reset
    end
end

# Appliquer le test à tous les types d'analyseurs
for analyzer_type in [EntropyAnalyzer, ComplexityAnalyzer]
    test_analyzer_interface(analyzer_type)
end
```

## 6. Tests de Régression

### Sauvegarde de Résultats de Référence
```julia
using JSON

# Sauvegarder des résultats de référence
function save_reference_results()
    results = Dict()
    
    analyzer = EntropyAnalyzer(2.0)
    for (name, data) in [
        ("simple", [1, 2, 2, 3]),
        ("uniform", repeat(1:5, 10)),
        ("skewed", vcat(repeat([1], 30), repeat(2:5, 5)))
    ]
        results[name] = calculate_entropy(analyzer, data)
    end
    
    open("test/fixtures/reference_results.json", "w") do f
        JSON.print(f, results, 4)
    end
end

# Tester contre les résultats de référence
@testset "Regression Tests" begin
    reference = JSON.parsefile("test/fixtures/reference_results.json")
    analyzer = EntropyAnalyzer(2.0)
    
    @test calculate_entropy(analyzer, [1, 2, 2, 3]) ≈ reference["simple"]
    @test calculate_entropy(analyzer, repeat(1:5, 10)) ≈ reference["uniform"]
    
    skewed_data = vcat(repeat([1], 30), repeat(2:5, 5))
    @test calculate_entropy(analyzer, skewed_data) ≈ reference["skewed"]
end
```

## 7. Tests Paramétriques

### Tests avec Différents Paramètres
```julia
@testset "Parametric Tests" begin
    test_cases = [
        (base=2.0, expected_max=log2(10)),
        (base=ℯ, expected_max=log(10)),
        (base=10.0, expected_max=log10(10))
    ]
    
    uniform_data = repeat(1:10, 10)
    
    for (base, expected_max) in test_cases
        @testset "Base $base" begin
            analyzer = EntropyAnalyzer(base)
            entropy = calculate_entropy(analyzer, uniform_data)
            @test entropy ≈ expected_max atol=1e-10
        end
    end
end
```

## 8. Couverture de Code

### Mesurer la Couverture
```julia
# Utiliser Pkg.test avec couverture
# julia --code-coverage=user -e 'using Pkg; Pkg.test(coverage=true)'

# Ou avec Coverage.jl
using Coverage

# Analyser la couverture après les tests
coverage = process_folder()
covered_lines, total_lines = get_summary(coverage)
percentage = covered_lines / total_lines * 100

@info "Code coverage: $(round(percentage, digits=1))% ($covered_lines/$total_lines lines)"
```

## 9. Tests d'Intégration Continue

### Configuration GitHub Actions
```yaml
# .github/workflows/CI.yml
name: CI
on:
  push:
    branches: [main]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        julia-version: ['1.6', '1.9', 'nightly']
    
    steps:
    - uses: actions/checkout@v2
    - uses: julia-actions/setup-julia@v1
      with:
        version: ${{ matrix.julia-version }}
    - uses: julia-actions/julia-buildpkg@v1
    - uses: julia-actions/julia-runtest@v1
    - uses: julia-actions/julia-processcoverage@v1
    - uses: codecov/codecov-action@v1
```

## 10. Bonnes Pratiques Générales

### Organisation et Lisibilité
```julia
@testset "Clear Test Organization" begin
    # Grouper les tests logiquement
    @testset "Input Validation" begin
        # Tests de validation
    end
    
    @testset "Core Functionality" begin
        # Tests de fonctionnalité principale
    end
    
    @testset "Edge Cases" begin
        # Tests de cas limites
    end
end
```

### Messages d'Erreur Informatifs
```julia
@testset "Informative Error Messages" begin
    data = [1, 2, 3]
    expected = 1.585
    actual = calculate_entropy(EntropyAnalyzer(2.0), data)
    
    @test actual ≈ expected atol=1e-3 "Entropy calculation failed: expected $expected, got $actual"
end
```

### Tests Déterministes
```julia
@testset "Deterministic Tests" begin
    # Fixer la graine pour la reproductibilité
    Random.seed!(42)
    
    data = rand(1:10, 100)
    result1 = calculate_entropy(EntropyAnalyzer(2.0), data)
    
    Random.seed!(42)
    data2 = rand(1:10, 100)
    result2 = calculate_entropy(EntropyAnalyzer(2.0), data2)
    
    @test result1 == result2
end
```
