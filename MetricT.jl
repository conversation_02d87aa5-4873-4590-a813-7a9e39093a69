"""
MODULE JULIA AUTONOME - MetricT
===============================

MÉTRIQUE AVEC DÉPENDANCES - MetricT (PRIORITÉ 3)
FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.

FORMULE : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]

USAGE :
    using MetricT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule4B_entropie_metrique_theo(formulas, sequence, 3)

DÉPENDANCES INCLUSES : calculer_formule1B_shannon_jointe_theo (ShannonT) - Module complètement autonome
"""

module MetricT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION DÉPENDANCE - ShannonT (INCLUSE POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
Fonction ShannonT incluse pour rendre le module MetricT autonome.
Calcule l'entropie de Shannon théorique pour une séquence croissante [main 1 : main n].
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p_theo(x) log₂(p_theo(x))
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - MetricT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie métrique Kolmogorov-Sinai théorique pondérée.
Mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.

FORMULE : MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)

Où :
- Complexité_pondérée(k) = (2/(k(k+1))) × ∑ᵢ₌₁ᵏ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
- H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = entropie conditionnelle théorique

CALCUL DES ENTROPIES CONDITIONNELLES :
- H_theo(X₁) = -log₂(p_theo(x₁)) (pas de conditionnement)
- H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁) (règle de chaîne)

PONDÉRATION :
- Chaque terme H_theo(Xᵢ | X₁,...,Xᵢ₋₁) est pondéré par i (position dans la séquence)
- Normalisation par (2/(k(k+1))) pour obtenir une moyenne pondérée

INTERPRÉTATION :
- MetricT > 0 : L'ajout du n-ème élément augmente la complexité pondérée
- MetricT < 0 : L'ajout du n-ème élément diminue la complexité pondérée
- MetricT ≈ 0 : L'ajout du n-ème élément n'affecte pas significativement la complexité

USAGE EN ANALYSE PRÉDICTIVE :
- Détection de changements de régime dans la complexité
- Mesure de l'impact informationnel de chaque nouvelle observation
- Évaluation de la contribution marginale à la complexité globale

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n] (doit être ≥ 2)

RETOUR :
- Différence d'entropie métrique pondérée en bits (si base=2.0)
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            # Utilise la fonction ShannonT incluse dans ce module
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    # Mesure l'impact de l'ajout du n-ème élément sur la complexité globale
    return complexite_n - complexite_n_minus_1
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule4B_entropie_metrique_theo

end # module MetricT
