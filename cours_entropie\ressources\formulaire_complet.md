# 📋 FORMULAIRE COMPLET - ENTROPIE
## Toutes les Formules Mathématiques Expliquées

### 🎯 Organisation du Formulaire
- **Niveau 1** : Formules de base (Shannon, conditionnelle)
- **Niveau 2** : Formules intermédiaires (KL, information mutuelle)
- **Niveau 3** : Formules avancées (métrique, topologique)
- **Annexes** : Propriétés, inégalités, cas particuliers

---

## 🟢 NIVEAU 1 : FORMULES DE BASE

### 1.1 Entropie de Shannon

**Formule principale** :
```
H(X) = -∑ p(x) log₂ p(x)
       x∈X
```

**Chaque symbole** :
- **H(X)** : Entropie de la variable aléatoire X (en bits)
- **∑** : Somme sur tous les événements possibles
- **p(x)** : Probabilité de l'événement x
- **log₂** : Logarithme en base 2
- **-** : Signe moins pour obtenir un résultat positif

**Cas particuliers** :
- **Distribution uniforme** : H(X) = log₂(n) où n = |X|
- **Événement certain** : H(X) = 0 si p(x₀) = 1
- **Convention** : 0 log 0 = 0

### 1.2 Entropie Conditionnelle

**Formule principale** :
```
H(Y|X) = ∑ p(x) H(Y|X=x)
         x∈X
```

**Développement** :
```
H(Y|X) = -∑∑ p(x,y) log₂ p(y|x)
          x y
```

**Chaque symbole** :
- **H(Y|X)** : Entropie de Y sachant X
- **p(x,y)** : Probabilité jointe
- **p(y|x)** : Probabilité conditionnelle

**Propriété fondamentale** :
```
H(Y|X) ≤ H(Y)
```

### 1.3 Entropie Jointe

**Formule** :
```
H(X,Y) = -∑∑ p(x,y) log₂ p(x,y)
          x y
```

**Règle de chaîne** :
```
H(X,Y) = H(X) + H(Y|X) = H(Y) + H(X|Y)
```

---

## 🟡 NIVEAU 2 : FORMULES INTERMÉDIAIRES

### 2.1 Information Mutuelle

**Formule principale** :
```
I(X;Y) = ∑∑ p(x,y) log₂ (p(x,y)/(p(x)p(y)))
         x y
```

**Formules équivalentes** :
```
I(X;Y) = H(X) - H(X|Y)
I(X;Y) = H(Y) - H(Y|X)
I(X;Y) = H(X) + H(Y) - H(X,Y)
I(X;Y) = D(p(x,y)||p(x)p(y))
```

**Propriétés** :
- **Symétrie** : I(X;Y) = I(Y;X)
- **Positivité** : I(X;Y) ≥ 0
- **Indépendance** : I(X;Y) = 0 ⟺ X ⊥ Y

### 2.2 Divergence de Kullback-Leibler

**Formule principale** :
```
D(p||q) = ∑ p(x) log₂ (p(x)/q(x))
          x
```

**Chaque symbole** :
- **D(p||q)** : Divergence de p vers q
- **p(x)** : Distribution "vraie"
- **q(x)** : Distribution "approximative"

**Propriétés** :
- **Positivité** : D(p||q) ≥ 0
- **Non-symétrie** : D(p||q) ≠ D(q||p)
- **Convexité** : Fonction convexe

**Relation avec entropie croisée** :
```
D(p||q) = H(p,q) - H(p)
```

### 2.3 Entropie Croisée

**Formule** :
```
H(p,q) = -∑ p(x) log₂ q(x)
         x
```

**Interprétation** : Longueur moyenne de codage avec distribution q quand la vraie distribution est p.

### 2.4 Entropie de Rényi

**Formule générale** :
```
H_α(X) = (1/(1-α)) log₂ (∑ p(x)^α)
                          x
```

**Cas particuliers** :
- **α → 1** : H₁(X) = H(X) (Shannon)
- **α = 0** : H₀(X) = log₂|X| (Hartley)
- **α = 2** : H₂(X) = -log₂(∑p(x)²) (Collision)
- **α → ∞** : H∞(X) = -log₂(max p(x)) (Min-entropie)

---

## 🔴 NIVEAU 3 : FORMULES AVANCÉES

### 3.1 Entropie Métrique (Kolmogorov-Sinai)

**Définition pour une partition** :
```
h_μ(T,α) = lim (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
           n→∞
```

**Entropie métrique** :
```
h_μ(T) = sup h_μ(T,α)
         α
```

**Chaque symbole** :
- **h_μ(T)** : Entropie métrique de la transformation T
- **α** : Partition mesurable finie
- **⋁** : Join (raffinement) de partitions
- **T⁻ⁱα** : Image inverse de α par Tⁱ

### 3.2 Entropie Topologique

**Définition par recouvrements** :
```
h_top(T,U) = lim (1/n) log N(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱU)
             n→∞
```

**Entropie topologique** :
```
h_top(T) = sup h_top(T,U)
           U
```

**Définition par ensembles séparés** :
```
h_top(T) = lim lim (1/n) log s_n(ε)
           ε→0 n→∞
```

### 3.3 Principe Variationnel

**Théorème fondamental** :
```
h_top(T) = sup{h_μ(T) : μ mesure T-invariante}
```

### 3.4 Théorème de Shannon-McMillan-Breiman

**Convergence ponctuelle** :
```
lim -(1/n) log μ([X₀,...,Xₙ₋₁](x)) = h_μ(T)
n→∞
```

pour μ-presque tout x.

---

## 📊 INÉGALITÉS FONDAMENTALES

### Inégalité de Jensen

**Pour fonction convexe φ** :
```
φ(∑ pᵢxᵢ) ≤ ∑ pᵢφ(xᵢ)
```

**Application** : Prouve D(p||q) ≥ 0

### Inégalité de Gibbs

**Énoncé** :
```
-∑ p(x) log q(x) ≥ -∑ p(x) log p(x)
```

**Équivalent** : H(p,q) ≥ H(p)

### Inégalité de Log-Somme

**Formule** :
```
∑ aᵢ log(aᵢ/bᵢ) ≥ (∑aᵢ) log((∑aᵢ)/(∑bᵢ))
```

### Inégalité de Sous-Additivité

**Entropie jointe** :
```
H(X₁,...,Xₙ) ≤ ∑ H(Xᵢ)
```

**Égalité** : Si et seulement si les variables sont indépendantes.

---

## 🧮 CAS PARTICULIERS IMPORTANTS

### Distribution de Bernoulli

**Paramètre** : p ∈ [0,1]
```
h(p) = -p log₂ p - (1-p) log₂(1-p)
```

**Maximum** : h(1/2) = 1 bit

### Distribution Uniforme

**Sur n éléments** :
```
H = log₂ n
```

### Distribution Géométrique

**Paramètre** : p ∈ (0,1)
```
H = -(1-p) log₂(1-p) - p log₂ p / (1-p)
```

### Distribution Exponentielle

**Entropie différentielle** :
```
h(X) = 1 + log λ
```

où λ est le paramètre de taux.

---

## 🔄 TRANSFORMATIONS ET CODAGES

### Théorème de Codage de Source

**Première forme** : Pour un code sans préfixe,
```
H(X) ≤ L̄ < H(X) + 1
```

où L̄ est la longueur moyenne de codage.

### Inégalité de Kraft

**Condition nécessaire et suffisante** :
```
∑ 2^(-lᵢ) ≤ 1
```

pour l'existence d'un code sans préfixe avec longueurs lᵢ.

### Codage de Huffman

**Longueur optimale** :
```
L_Huffman = H(X) + p_max
```

où p_max est la probabilité maximale.

---

## 🎯 FORMULES DE CALCUL PRATIQUE

### Approximation de Stirling

**Pour grandes valeurs** :
```
log n! ≈ n log n - n log e + O(log n)
```

### Entropie Empirique

**Estimateur** :
```
Ĥ(X) = -(1/n) ∑ nᵢ log₂(nᵢ/n)
```

où nᵢ est le nombre d'occurrences de xᵢ.

### Correction de Biais

**Estimateur corrigé** :
```
Ĥ_corrigé = Ĥ + (k-1)/(2n ln 2)
```

où k est le nombre de symboles distincts.

---

## 🔗 RELATIONS ENTRE FORMULES

### Diagramme des Relations

```
H(X,Y) = H(X) + H(Y|X) = H(Y) + H(X|Y)
    ↓
I(X;Y) = H(X) + H(Y) - H(X,Y)
    ↓
I(X;Y) = D(p(x,y)||p(x)p(y))
    ↓
D(p||q) = H(p,q) - H(p)
```

### Identités Fondamentales

```
H(X|Y) = H(X,Y) - H(Y)
I(X;Y) = H(X) - H(X|Y)
D(p||q) = ∑ p(x) log(p(x)) - ∑ p(x) log(q(x))
h_μ(T) = lim h_μ(T,α_n) pour α_n génératrice
```

---

## 📚 CONVENTIONS ET NOTATIONS

### Logarithmes
- **log₂** : Base 2 (bits)
- **ln** : Base e (nats)
- **log₁₀** : Base 10 (dits)

### Probabilités
- **p(x)** : Probabilité marginale
- **p(x,y)** : Probabilité jointe
- **p(y|x)** : Probabilité conditionnelle

### Mesures
- **μ** : Mesure de probabilité
- **λ** : Mesure de Lebesgue
- **δ_x** : Mesure de Dirac

### Transformations
- **T** : Transformation mesurable
- **σ** : Décalage (shift)
- **f** : Application continue

---

*Ce formulaire contient toutes les formules essentielles de la théorie de l'entropie, organisées par niveau de complexité.*
