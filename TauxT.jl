"""
MODULE JULIA AUTONOME - TauxT
=============================

MÉTRIQUE COMPLEXE - TauxT (PRIORITÉ 4)
FORMULE 3B : Taux d'Entropie (VERSION OPTIMISÉE)
Calcule le taux d'entropie théorique en utilisant l'entropie de bloc.

FORMULE : TauxT_n = (1/n) × BlockT_n = (1/n) × ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Relation directe avec BlockT pour éviter la duplication de code.

USAGE :
    using TauxT
    sequence_complete = ["0_A_BANKER", "1_B_PLAYER", ...]  # Séquence complète pour calculs empiriques
    formulas = FormulasTheoretical{Float64}(2.0, 1e-12, sequence_complete)
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule3B_taux_entropie_theo(formulas, sequence, 3)

DÉPENDANCES INCLUSES : calculer_formule10B_block_cumulative_theo (BlockT) + Module Support Probabilités complet - Module complètement autonome
"""

module TauxT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE SUPPORT PROBABILITÉS (INCLUS POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
SUPPORT PROBABILITÉS - Niveau 1 (Base)
Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations.
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    if isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
SUPPORT PROBABILITÉS - Niveau 2
Calcule la probabilité jointe théorique d'une séquence en utilisant uniquement les probabilités théoriques INDEX5.
Hypothèse d'indépendance : p(x₁, x₂, ..., xₙ) = p_théo(x₁) × p_théo(x₂) × ... × p_théo(xₙ)
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    # Calculer la probabilité jointe comme produit des probabilités théoriques marginales
    joint_prob = one(T)

    for value in sequence
        # Utiliser la probabilité théorique INDEX5 pour chaque élément
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        joint_prob *= p_theo
    end

    return joint_prob
end

"""
SUPPORT PROBABILITÉS - Niveau 3
Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁).
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION DÉPENDANCE - BlockT (INCLUSE POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
Fonction BlockT incluse pour rendre le module TauxT autonome.
Calcule l'entropie de bloc selon la formule du cours d'entropie adaptée aux séquences observées.
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n < 3 || n > length(sequence)
        return zero(T)  # Besoin d'au moins 3 mains pour des sous-séquences de longueur 3
    end

    # Prendre la séquence des n premières mains : [main 1:main n]
    sequence_n = sequence[1:n]

    # Calculer l'entropie de bloc selon la formule du cours :
    # H_n = -∑ p_théo(séquence_observée) log₂ p_théo(séquence_observée)
    # Où ∑ porte sur toutes les sous-séquences de longueur 3 dans sequence_n

    block_entropy = zero(T)

    # Parcourir toutes les sous-séquences de longueur 3 dans la séquence [1:n]
    for start_pos in 1:(length(sequence_n) - 3 + 1)
        # Extraire la sous-séquence de longueur 3
        subseq = sequence_n[start_pos:start_pos + 2]

        # Calculer p_théo(sous-séquence) via probabilités jointes
        p_joint = calculer_probabilite_jointe_theo(formulas, subseq)

        # Appliquer la formule du cours : -p_théo(seq) × log₂(p_théo(seq))
        if p_joint > zero(T)
            block_entropy += -p_joint * (log(p_joint) / log(formulas.base))
        end
    end

    return block_entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - TauxT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule le taux d'entropie théorique en utilisant l'entropie de bloc.
Mesure l'entropie moyenne par sous-séquence de longueur 3 dans la séquence [1:n].

FORMULE CORRIGÉE : TauxT_n = BlockT_n / (n - 2)

RELATION FONDAMENTALE : TauxT_n = BlockT_n / nombre_de_sous_séquences
- BlockT_n = entropie de bloc des sous-séquences de longueur 3 dans [main 1:main n]
- TauxT_n = entropie moyenne par sous-séquence de longueur 3
- Nombre de sous-séquences = n - 2 (pour des sous-séquences de longueur 3)

ÉQUIVALENCE AVEC CondT : TauxT_n = CondT_n
- Les deux métriques calculent la même chose : l'entropie conditionnelle moyenne
- TauxT utilise BlockT puis divise par n
- CondT calcule directement la moyenne des entropies conditionnelles

INTERPRÉTATION PRÉDICTIVE :
- TauxT faible : Système très prévisible, peu d'information nouvelle par élément
- TauxT élevé : Système imprévisible, beaucoup d'information nouvelle par élément
- TauxT → 0 : Système déterministe (chaque élément parfaitement prédit)
- TauxT → H_max : Système complètement aléatoire

CONVERGENCE ASYMPTOTIQUE :
- Pour les processus stationnaires : lim(n→∞) TauxT_n = taux d'entropie du processus
- Mesure la complexité informationnelle intrinsèque du système INDEX5
- Indicateur de la limite théorique de prédictibilité

USAGE EN ANALYSE PRÉDICTIVE :
- PRIORITÉ 4 : Métrique importante pour évaluer la complexité moyenne
- Complément de CondT pour validation croisée
- Base pour estimer les performances limites des prédicteurs
- Mesure de l'efficacité informationnelle du système

PROPRIÉTÉS MATHÉMATIQUES :
- Toujours ≥ 0 (propriété de l'entropie)
- TauxT_n ≤ TauxT_{n-1} généralement (convergence)
- Relation : BlockT_n = n × TauxT_n

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5 et séquence complète
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Taux d'entropie (entropie moyenne par élément) en bits (si base=2.0)
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n < 3 || n > length(sequence)
        return zero(T)  # Besoin d'au moins 3 mains pour calculer TauxT
    end

    # Calculer l'entropie de bloc en utilisant la fonction BlockT incluse
    # BlockT_n = entropie des sous-séquences de longueur 3 dans [main 1:main n]
    block_entropy = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)

    # Calculer le nombre de sous-séquences de longueur 3
    nb_subsequences = n - 2

    # Retourner le taux d'entropie : TauxT_n = BlockT_n / (n - 2)
    # Représente l'entropie moyenne par sous-séquence de longueur 3
    return block_entropy / T(nb_subsequences)
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule3B_taux_entropie_theo

end # module TauxT
