"""
MODULE JULIA AUTONOME - CrossT
==============================

MÉTRIQUE AUTONOME - CrossT (PRIORITÉ 2)
FORMULE 8B : Entropie Croisée Observée vs <PERSON><PERSON><PERSON><PERSON><PERSON> (VERSION CORRIGÉE)
Mesure le coût d'encodage des données réellement observées en utilisant les probabilités théoriques INDEX5 comme modèle de codage.

FORMULE : CrossT = -∑ p_obs(x) × log₂ p_theo(x)
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "de codage" (modèle)

USAGE :
    using CrossT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule8B_entropie_croisee_theo(formulas, sequence, 3)

DÉPENDANCES : AUCUNE - Module complètement autonome
"""

module CrossT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - CrossT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie croisée entre la distribution observée et la distribution théorique INDEX5.

FORMULE : CrossT_n = -∑_{x ∈ E_n} p_obs(x) × log₂(p_theo(x))
Où :
- E_n = ensemble des valeurs INDEX5 distinctes observées dans [1:n]
- p_obs(x) = fréquence observée de x dans [1:n]
- p_theo(x) = probabilité théorique INDEX5 de x

INTERPRÉTATION :
- CrossT mesure le coût d'encodage des données observées avec le modèle théorique
- CrossT ≥ H_obs (entropie observée) - égalité ssi distributions identiques
- Plus CrossT est élevé, plus le modèle théorique est inefficace pour encoder les données observées
- Relation : DivKL = CrossT - H_obs (où H_obs = entropie de Shannon observée)

USAGE EN THÉORIE DE L'INFORMATION :
- Évaluation de la qualité d'un modèle de codage
- Mesure de l'inefficacité du modèle théorique
- Optimisation de systèmes de compression

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Entropie croisée en bits (si base=2.0), toujours ≥ 0
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée : -∑ p_obs(x) × log₂ p_theo(x)
    cross_entropy = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Entropie croisée standard : -p_obs × log₂(p_theo)
            # Formule officielle : H(P,Q) = -∑ p(x) log q(x)
            cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
        end
    end

    return cross_entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule8B_entropie_croisee_theo

end # module CrossT
